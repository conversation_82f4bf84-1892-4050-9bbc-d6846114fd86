import mongoose from 'mongoose';
import User from '../models/userModel.js';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const envPath = path.resolve(__dirname, '../../.env');
dotenv.config({ path: envPath });

const updateProfileCompletion = async () => {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGO_URI);
        console.log('Connected to MongoDB');

        // Find all users who don't have isProfileComplete set to true
        const users = await User.find({
            $or: [
                { isProfileComplete: { $ne: true } },
                { isProfileComplete: { $exists: false } }
            ]
        });

        console.log(`Found ${users.length} users to check`);

        let updatedCount = 0;

        for (const user of users) {
            // Check if user has required fields for a complete profile
            const hasRequiredFields = user.name && user.username && user.email;
            
            if (hasRequiredFields) {
                // Update the user to mark profile as complete
                await User.findByIdAndUpdate(user._id, { 
                    isProfileComplete: true 
                });
                
                console.log(`✅ Updated profile completion for user: ${user.username} (${user.email})`);
                updatedCount++;
            } else {
                console.log(`❌ User ${user.username || user.email} missing required fields:`, {
                    hasName: !!user.name,
                    hasUsername: !!user.username,
                    hasEmail: !!user.email
                });
            }
        }

        console.log(`\n🎉 Updated ${updatedCount} users' profile completion status`);
        
        // Verify the updates
        const completeProfiles = await User.countDocuments({ isProfileComplete: true });
        const incompleteProfiles = await User.countDocuments({ isProfileComplete: { $ne: true } });
        
        console.log(`\n📊 Profile Status Summary:`);
        console.log(`   Complete profiles: ${completeProfiles}`);
        console.log(`   Incomplete profiles: ${incompleteProfiles}`);

    } catch (error) {
        console.error('Error updating profile completion:', error);
    } finally {
        // Close the connection
        await mongoose.connection.close();
        console.log('\nDisconnected from MongoDB');
        process.exit(0);
    }
};

// Run the script
updateProfileCompletion();
